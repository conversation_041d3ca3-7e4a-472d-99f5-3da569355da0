import React, { useRef, useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import { X, Mic, MicOff, RotateCcw } from 'lucide-react';
import { toast } from 'react-hot-toast';
import './MobileScanner.css';

const MobileScanner = ({ onComplete, onExit }) => {
    const videoRef = useRef(null);
    const canvasRef = useRef(null);
    const animationFrameId = useRef(null);
    const streamRef = useRef(null);

    const [isVoiceEnabled, setIsVoiceEnabled] = useState(true);
    const [cameraStatus, setCameraStatus] = useState("initializing");
    const [currentInstruction, setCurrentInstruction] = useState("Getting camera ready...");
    const [scanProgress, setScanProgress] = useState(0);
    const [completedScans, setCompletedScans] = useState([]);
    const [isComplete, setIsComplete] = useState(false);
    const [flashEnabled, setFlashEnabled] = useState(true);
    const [currentRegionIndex, setCurrentRegionIndex] = useState(0);
    const [isLandscape, setIsLandscape] = useState(false);
    const [skipOrientationCheck, setSkipOrientationCheck] = useState(false);
    const [debugInfo, setDebugInfo] = useState('');

    // Force landscape orientation and check orientation
    useEffect(() => {
        const checkOrientation = () => {
            // More reliable landscape detection
            const aspectRatio = window.innerWidth / window.innerHeight;
            const isLandscapeBySize = window.innerWidth > window.innerHeight;

            // Check screen orientation API if available
            let isLandscapeByOrientation = false;
            if (window.screen && window.screen.orientation) {
                const angle = window.screen.orientation.angle;
                isLandscapeByOrientation = angle === 90 || angle === 270;
            }

            // Fallback to deprecated orientation property
            // eslint-disable-next-line no-restricted-globals
            const orientationAngle = window.orientation;
            let isLandscapeByLegacy = false;
            if (typeof orientationAngle !== 'undefined') {
                isLandscapeByLegacy = orientationAngle === 90 || orientationAngle === -90;
            }

            const isLandscapeMode = isLandscapeBySize || isLandscapeByOrientation || isLandscapeByLegacy;

            console.log('Orientation check:', {
                innerWidth: window.innerWidth,
                innerHeight: window.innerHeight,
                aspectRatio: aspectRatio.toFixed(2),
                isLandscapeBySize,
                isLandscapeByOrientation,
                isLandscapeByLegacy,
                finalIsLandscape: isLandscapeMode
            });

            setIsLandscape(isLandscapeMode);
        };

        // Lock to landscape if possible
        // eslint-disable-next-line no-restricted-globals
        if (screen.orientation && screen.orientation.lock) {
            // eslint-disable-next-line no-restricted-globals
            screen.orientation.lock('landscape').catch(err => {
                console.log('Orientation lock not supported:', err);
            });
        }

        checkOrientation();

        // Use multiple event listeners for better detection
        window.addEventListener('orientationchange', () => {
            // Add delay to ensure orientation change is complete
            setTimeout(checkOrientation, 100);
        });
        window.addEventListener('resize', checkOrientation);

        // Also check periodically in case events don't fire
        const orientationInterval = setInterval(checkOrientation, 1000);

        return () => {
            window.removeEventListener('orientationchange', checkOrientation);
            window.removeEventListener('resize', checkOrientation);
            clearInterval(orientationInterval);

            // Unlock orientation when leaving
            // eslint-disable-next-line no-restricted-globals
            if (screen.orientation && screen.orientation.unlock) {
                // eslint-disable-next-line no-restricted-globals
                screen.orientation.unlock();
            }
        };
    }, []);

    const regions = [
        { id: "center", name: "Front Teeth", description: "Center your front teeth in the frame" },
        { id: "right", name: "Right Side", description: "Show your right molars" },
        { id: "left", name: "Left Side", description: "Show your left molars" },
        { id: "upper", name: "Upper Arch", description: "Open wide to show upper teeth" },
        { id: "lower", name: "Lower Arch", description: "Show your lower teeth clearly" }
    ];

    // Refs for scanning state that shouldn't trigger re-renders
    const lastInstructionTimeRef = useRef(0);
    const detectionConfidenceRef = useRef(0);
    const steadySinceRef = useRef(null);

    useEffect(() => {
        if (isComplete || cameraStatus !== "ready") return;

        const instructionDelay = 3000;
        const scanDelay = 2000;
        const confidenceThreshold = 0.6;

        const currentRegion = regions[currentRegionIndex];
        const videoElement = videoRef.current;
        const canvasElement = canvasRef.current;
        const ctx = canvasElement?.getContext('2d');

        const speak = (message) => {
            if (!isVoiceEnabled) return;
            const now = Date.now();
            if (now - lastInstructionTimeRef.current > instructionDelay) {
                window.speechSynthesis.cancel();
                const utterance = new SpeechSynthesisUtterance(message);
                utterance.rate = 0.9;
                utterance.pitch = 1.1;
                window.speechSynthesis.speak(utterance);
                lastInstructionTimeRef.current = now;
            }
        };

        const updateInstruction = (text) => {
            setCurrentInstruction(text);
        };

        const captureScan = () => {
            const dataUrl = canvasElement.toDataURL('image/jpeg');
            const newCapture = {
                id: `${currentRegion.id}-${Date.now()}`,
                regionName: currentRegion.name,
                imageData: dataUrl,
                timestamp: new Date()
            };

            setCompletedScans(prev => [...prev, newCapture]);
            toast.success(`${currentRegion.name} captured!`, { icon: '✅' });

            const nextIndex = currentRegionIndex + 1;
            setCurrentRegionIndex(nextIndex);
            setScanProgress((nextIndex / regions.length) * 100);

            if (nextIndex < regions.length) {
                steadySinceRef.current = null;
                updateInstruction(regions[nextIndex].description);
                speak(`Scan successful. Now, show me your ${regions[nextIndex].name}.`);
            } else {
                updateInstruction("Scan complete! Well done.");
                speak("All scans are complete. Great job!");
                setIsComplete(true);
                cancelAnimationFrame(animationFrameId.current);

                // Auto-return to dashboard after 3 seconds
                setTimeout(() => {
                    handleComplete();
                }, 3000);
            }
        };

        const processVideoFrame = () => {
            if (!videoElement || !canvasElement || !ctx || isComplete) return;

            const { videoWidth, videoHeight } = videoElement;
            canvasElement.width = videoWidth;
            canvasElement.height = videoHeight;

            ctx.drawImage(videoElement, 0, 0, videoWidth, videoHeight);

            // Define bounding boxes for each region (same as original)
            const regionBoxes = {
                center: { x: videoWidth * 0.25, y: videoHeight * 0.3, width: videoWidth * 0.5, height: videoHeight * 0.4 },
                right: { x: videoWidth * 0.1, y: videoHeight * 0.3, width: videoWidth * 0.4, height: videoHeight * 0.4 },
                left: { x: videoWidth * 0.5, y: videoHeight * 0.3, width: videoWidth * 0.4, height: videoHeight * 0.4 },
                upper: { x: videoWidth * 0.2, y: videoHeight * 0.2, width: videoWidth * 0.6, height: videoHeight * 0.3 },
                lower: { x: videoWidth * 0.2, y: videoHeight * 0.5, width: videoWidth * 0.6, height: videoHeight * 0.3 }
            };

            const activeBox = regionBoxes[currentRegion.id];

            // Simple detection logic (same as original)
            const imageData = ctx.getImageData(activeBox.x, activeBox.y, activeBox.width, activeBox.height);
            const data = imageData.data;
            let brightnessSum = 0;
            let highContrastPixels = 0;

            for (let i = 0; i < data.length; i += 4) {
                const r = data[i];
                const g = data[i + 1];
                const b = data[i + 2];
                const brightness = (r + g + b) / 3;
                brightnessSum += brightness;
                if (brightness > 180) highContrastPixels++;
            }

            const avgBrightness = brightnessSum / (data.length / 4);
            const contrastRatio = highContrastPixels / (data.length / 4);
            detectionConfidenceRef.current = Math.min((avgBrightness / 255) * 0.7 + contrastRatio * 0.3, 1);

            // Draw bounding boxes (same as original)
            Object.entries(regionBoxes).forEach(([regionId, box]) => {
                const isCurrent = regionId === currentRegion.id;
                ctx.lineWidth = isCurrent ? 4 : 2;

                if (isCurrent) {
                    const progress = steadySinceRef.current ? (Date.now() - steadySinceRef.current) / scanDelay : 0;
                    const color = detectionConfidenceRef.current > confidenceThreshold ? `rgba(0, 255, 136, ${0.5 + detectionConfidenceRef.current * 0.5})` : `rgba(255, 255, 255, 0.7)`;
                    ctx.strokeStyle = color;
                    ctx.strokeRect(box.x, box.y, box.width, box.height);

                    // Draw progress bar
                    if(progress > 0) {
                        ctx.fillStyle = "#00ff88";
                        ctx.fillRect(box.x, box.y + box.height + 5, box.width * Math.min(progress, 1), 5);
                    }
                } else {
                    ctx.strokeStyle = "#ffffff50";
                    ctx.strokeRect(box.x, box.y, box.width, box.height);
                }
            });

            // Detection logic (same as original)
            if (detectionConfidenceRef.current > confidenceThreshold) {
                if (!steadySinceRef.current) {
                    steadySinceRef.current = Date.now();
                    updateInstruction("Perfect! Hold steady...");
                    speak("Perfect position, hold steady.");
                } else {
                    const elapsed = Date.now() - steadySinceRef.current;
                    if (elapsed >= scanDelay) {
                        captureScan();
                        return; // Exit early to prevent further processing
                    }
                }
            } else {
                if (steadySinceRef.current) {
                    updateInstruction(currentRegion.description);
                    speak("Please reposition.");
                }
                steadySinceRef.current = null;
            }

            animationFrameId.current = requestAnimationFrame(processVideoFrame);
        };



        if (videoElement && canvasElement && cameraStatus === "ready") {
            animationFrameId.current = requestAnimationFrame(processVideoFrame);
        }

        return () => {
            if (animationFrameId.current) {
                cancelAnimationFrame(animationFrameId.current);
            }
        };
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [isVoiceEnabled, currentRegionIndex, isComplete, cameraStatus]);

    // Separate effect for camera initialization
    useEffect(() => {
        const initializeCamera = async () => {
            try {
                // Check if mediaDevices is available
                if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
                    throw new Error("Camera not supported on this device");
                }

                console.log("Starting camera initialization...");

                // Try multiple constraint configurations for better compatibility
                const constraintOptions = [
                    // Option 1: Rear camera with high resolution
                    {
                        video: {
                            facingMode: 'environment',
                            width: { ideal: 1920 },
                            height: { ideal: 1080 }
                        }
                    },
                    // Option 2: Rear camera with medium resolution
                    {
                        video: {
                            facingMode: 'environment',
                            width: { ideal: 1280 },
                            height: { ideal: 720 }
                        }
                    },
                    // Option 3: Any camera with basic resolution
                    {
                        video: {
                            width: { ideal: 1280 },
                            height: { ideal: 720 }
                        }
                    },
                    // Option 4: Most basic constraints
                    {
                        video: true
                    }
                ];

                let stream = null;
                let lastError = null;

                for (let i = 0; i < constraintOptions.length; i++) {
                    try {
                        console.log(`Trying camera constraints option ${i + 1}:`, constraintOptions[i]);
                        setDebugInfo(`Trying camera option ${i + 1}...`);
                        stream = await navigator.mediaDevices.getUserMedia(constraintOptions[i]);
                        console.log(`Camera stream obtained with option ${i + 1}`);
                        setDebugInfo(`Camera stream obtained with option ${i + 1}`);
                        break;
                    } catch (error) {
                        console.log(`Option ${i + 1} failed:`, error);
                        setDebugInfo(`Option ${i + 1} failed: ${error.message}`);
                        lastError = error;
                        continue;
                    }
                }

                if (!stream) {
                    throw lastError || new Error("All camera constraint options failed");
                }

                streamRef.current = stream;

                // Enable flash/torch if available
                const track = stream.getVideoTracks()[0];
                console.log("Video track settings:", track.getSettings());
                console.log("Video track capabilities:", track.getCapabilities?.());

                if (track && 'applyConstraints' in track && flashEnabled) {
                    try {
                        const capabilities = track.getCapabilities();
                        if (capabilities && capabilities.torch) {
                            await track.applyConstraints({
                                advanced: [{ torch: true }]
                            });
                            console.log("Flash enabled successfully");
                        } else {
                            console.log("Flash not supported on this device");
                        }
                    } catch (torchError) {
                        console.log("Torch constraint failed:", torchError);
                        // Flash not supported, continue without it
                    }
                }

                const videoElement = videoRef.current;
                if (videoElement) {
                    console.log("Setting up video element...");

                    // Set video properties for better mobile compatibility
                    videoElement.setAttribute('playsinline', 'true');
                    videoElement.setAttribute('webkit-playsinline', 'true');
                    videoElement.muted = true;
                    videoElement.autoplay = true;

                    videoElement.srcObject = stream;

                    // Handle video events
                    videoElement.onloadedmetadata = () => {
                        console.log("Video metadata loaded, dimensions:", videoElement.videoWidth, "x", videoElement.videoHeight);
                    };

                    videoElement.oncanplay = () => {
                        console.log("Video can play");
                        videoElement.play().then(() => {
                            console.log("Video playing successfully");
                            setCameraStatus("ready");
                            setCurrentInstruction(regions[0].description);
                        }).catch(playError => {
                            console.error("Video play failed:", playError);
                            setCameraStatus("error");
                            toast.error("Failed to start camera preview", { duration: 6000 });
                        });
                    };

                    videoElement.onerror = (error) => {
                        console.error("Video element error:", error);
                        setCameraStatus("error");
                        toast.error("Camera preview error", { duration: 6000 });
                    };

                    // Force play attempt after a short delay
                    setTimeout(() => {
                        if (videoElement.readyState >= 2) { // HAVE_CURRENT_DATA
                            videoElement.play().catch(err => console.log("Auto-play failed:", err));
                        }
                    }, 500);
                }
            } catch (error) {
                console.error("Camera initialization failed:", error);
                setCameraStatus("error");

                let errorMessage = "Camera access failed. ";
                if (error.name === 'NotAllowedError') {
                    errorMessage += "Please allow camera permissions and refresh the page.";
                } else if (error.name === 'NotFoundError') {
                    errorMessage += "No camera found on this device.";
                } else if (error.name === 'NotSupportedError') {
                    errorMessage += "Camera not supported on this browser.";
                } else if (error.name === 'OverconstrainedError') {
                    errorMessage += "Camera constraints not supported. Try a different device.";
                } else {
                    errorMessage += `Error: ${error.message}`;
                }

                toast.error(errorMessage, { duration: 8000 });
            }
        };

        if (cameraStatus === "initializing") {
            // Add a small delay to ensure DOM is ready
            setTimeout(initializeCamera, 100);
        }

        return () => {
            if (streamRef.current) {
                streamRef.current.getTracks().forEach(track => {
                    track.stop();
                    console.log("Camera track stopped");
                });
            }
        };
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [flashEnabled, cameraStatus]);

    const handleComplete = () => {
        if (streamRef.current) {
            streamRef.current.getTracks().forEach(track => track.stop());
        }
        onComplete(completedScans);
    };

    const handleExit = () => {
        if (streamRef.current) {
            streamRef.current.getTracks().forEach(track => track.stop());
        }
        onExit();
    };

    const requestCameraPermission = async () => {
        try {
            // First check if permissions API is available
            if ('permissions' in navigator) {
                const permission = await navigator.permissions.query({ name: 'camera' });
                console.log('Camera permission status:', permission.state);

                if (permission.state === 'denied') {
                    toast.error('Camera permission denied. Please enable camera access in your browser settings.', { duration: 8000 });
                    return false;
                }
            }

            // Try to get camera access
            const stream = await navigator.mediaDevices.getUserMedia({
                video: { facingMode: 'environment' }
            });

            // Stop the test stream immediately
            stream.getTracks().forEach(track => track.stop());

            toast.success('Camera permission granted!', { duration: 3000 });
            return true;
        } catch (error) {
            console.error('Permission request failed:', error);
            toast.error('Failed to get camera permission. Please check your browser settings.', { duration: 6000 });
            return false;
        }
    };

    const restartScan = async () => {
        // Reset all scanning state
        setCompletedScans([]);
        setScanProgress(0);
        setIsComplete(false);
        setCurrentRegionIndex(0);
        setCurrentInstruction("Getting camera ready...");

        // Reset refs
        lastInstructionTimeRef.current = 0;
        detectionConfidenceRef.current = 0;
        steadySinceRef.current = null;

        // Cancel any ongoing animation frame
        if (animationFrameId.current) {
            cancelAnimationFrame(animationFrameId.current);
        }

        // Stop existing stream
        if (streamRef.current) {
            streamRef.current.getTracks().forEach(track => track.stop());
            streamRef.current = null;
        }

        // Request permission first if we're in error state
        if (cameraStatus === 'error') {
            const hasPermission = await requestCameraPermission();
            if (!hasPermission) {
                return; // Don't proceed if permission denied
            }
        }

        setCameraStatus("initializing");
    };

    // Show orientation prompt if not in landscape and not skipped
    if (!isLandscape && !skipOrientationCheck) {
        return (
            <div className="mobile-scanner orientation-prompt">
                <motion.div
                    className="orientation-content"
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.6 }}
                >
                    <div className="orientation-icon">📱➡️📱</div>
                    <h2>Rotate Your Device</h2>
                    <p>Please rotate your device to landscape mode for the best scanning experience</p>
                    <div className="orientation-buttons">
                        <button onClick={() => setSkipOrientationCheck(true)} className="skip-btn">
                            Continue Anyway
                        </button>
                        <button onClick={handleExit} className="exit-btn-small">
                            Go Back
                        </button>
                    </div>
                </motion.div>
            </div>
        );
    }

    if (isComplete) {
        return (
            <div className="mobile-scanner completion-screen">
                <motion.div
                    className="completion-content"
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.6 }}
                >
                    <div className="completion-icon">✅</div>
                    <h2>Scan Complete!</h2>
                    <p>All {completedScans.length} scans captured successfully</p>
                    <p className="auto-return">Returning to dashboard...</p>
                </motion.div>
            </div>
        );
    }

    return (
        <div className="mobile-scanner">
            {/* Header Controls */}
            <div className="scanner-header">
                <motion.button 
                    className="exit-btn"
                    onClick={handleExit}
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                >
                    <X size={24} />
                </motion.button>
                
                <div className="header-controls">
                    <motion.button 
                        className="control-btn"
                        onClick={() => setIsVoiceEnabled(!isVoiceEnabled)}
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.9 }}
                    >
                        {isVoiceEnabled ? <Mic size={20} /> : <MicOff size={20} />}
                    </motion.button>
                    
                    <motion.button 
                        className="control-btn"
                        onClick={restartScan}
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.9 }}
                    >
                        <RotateCcw size={20} />
                    </motion.button>
                </div>
            </div>

            {/* Camera View */}
            <div className="camera-container">
                <video
                    ref={videoRef}
                    autoPlay
                    playsInline
                    muted
                    className="camera-video"
                    style={{
                        display: cameraStatus === 'ready' ? 'block' : 'none',
                        backgroundColor: '#000'
                    }}
                />
                <canvas
                    ref={canvasRef}
                    className="camera-canvas"
                    style={{
                        display: cameraStatus === 'ready' ? 'block' : 'none'
                    }}
                />

                {/* Camera Status Indicator */}
                {cameraStatus === 'initializing' && (
                    <div className="camera-loading">
                        <div className="loading-spinner"></div>
                        <h3>Initializing Camera...</h3>
                        <p>Please allow camera access when prompted</p>
                    </div>
                )}
                
                {/* Instruction Overlay */}
                <div className="instruction-overlay">
                    <motion.div 
                        className="instruction-content"
                        key={currentInstruction}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.5 }}
                    >
                        <h2>{currentInstruction}</h2>
                        <div className="progress-info">
                            <span>Scan {currentRegionIndex + 1} of 5</span>
                            <div className="progress-bar">
                                <motion.div 
                                    className="progress-fill"
                                    initial={{ width: 0 }}
                                    animate={{ width: `${scanProgress}%` }}
                                    transition={{ duration: 0.5 }}
                                />
                            </div>
                        </div>
                    </motion.div>
                </div>

                {/* Debug Info */}
                {debugInfo && cameraStatus === 'initializing' && (
                    <div className="debug-info">
                        <p>{debugInfo}</p>
                    </div>
                )}

                {/* Camera Error */}
                {cameraStatus === 'error' && (
                    <div className="camera-error">
                        <h3>Camera Access Required</h3>
                        <p>This app needs camera access to scan your teeth. Please:</p>
                        <ul className="error-steps">
                            <li>Allow camera permissions when prompted</li>
                            <li>Make sure no other app is using the camera</li>
                            <li>Try refreshing the page if needed</li>
                        </ul>
                        {debugInfo && (
                            <div className="debug-details">
                                <p><strong>Debug info:</strong> {debugInfo}</p>
                            </div>
                        )}
                        <div className="error-buttons">
                            <button onClick={restartScan} className="retry-btn">
                                Try Again
                            </button>
                            <button onClick={handleExit} className="exit-error-btn">
                                Go Back
                            </button>
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
};

export default MobileScanner;
