import React, { useRef, useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import { X, Mic, MicOff, RotateCcw } from 'lucide-react';
import { toast } from 'react-hot-toast';
import './MobileScanner.css';

const MobileScanner = ({ onComplete, onExit }) => {
    const videoRef = useRef(null);
    const canvasRef = useRef(null);
    const animationFrameId = useRef(null);
    const streamRef = useRef(null);

    const [isVoiceEnabled, setIsVoiceEnabled] = useState(true);
    const [cameraStatus, setCameraStatus] = useState("initializing");
    const [currentInstruction, setCurrentInstruction] = useState("Getting camera ready...");
    const [scanProgress, setScanProgress] = useState(0);
    const [completedScans, setCompletedScans] = useState([]);
    const [isComplete, setIsComplete] = useState(false);
    const [flashEnabled, setFlashEnabled] = useState(true);
    const [currentRegionIndex, setCurrentRegionIndex] = useState(0);
    const [isLandscape, setIsLandscape] = useState(false);

    // Force landscape orientation and check orientation
    useEffect(() => {
        const checkOrientation = () => {
            const isLandscapeMode = window.innerWidth > window.innerHeight;
            setIsLandscape(isLandscapeMode);
        };

        // Lock to landscape if possible
        // eslint-disable-next-line no-restricted-globals
        if (screen.orientation && screen.orientation.lock) {
            // eslint-disable-next-line no-restricted-globals
            screen.orientation.lock('landscape').catch(err => {
                console.log('Orientation lock not supported:', err);
            });
        }

        checkOrientation();
        window.addEventListener('orientationchange', checkOrientation);
        window.addEventListener('resize', checkOrientation);

        return () => {
            window.removeEventListener('orientationchange', checkOrientation);
            window.removeEventListener('resize', checkOrientation);

            // Unlock orientation when leaving
            // eslint-disable-next-line no-restricted-globals
            if (screen.orientation && screen.orientation.unlock) {
                // eslint-disable-next-line no-restricted-globals
                screen.orientation.unlock();
            }
        };
    }, []);

    const regions = [
        { id: "center", name: "Front Teeth", description: "Center your front teeth in the frame" },
        { id: "right", name: "Right Side", description: "Show your right molars" },
        { id: "left", name: "Left Side", description: "Show your left molars" },
        { id: "upper", name: "Upper Arch", description: "Open wide to show upper teeth" },
        { id: "lower", name: "Lower Arch", description: "Show your lower teeth clearly" }
    ];

    // Refs for scanning state that shouldn't trigger re-renders
    const lastInstructionTimeRef = useRef(0);
    const detectionConfidenceRef = useRef(0);
    const steadySinceRef = useRef(null);

    useEffect(() => {
        if (isComplete || cameraStatus !== "ready") return;

        const instructionDelay = 3000;
        const scanDelay = 2000;
        const confidenceThreshold = 0.6;

        const currentRegion = regions[currentRegionIndex];
        const videoElement = videoRef.current;
        const canvasElement = canvasRef.current;
        const ctx = canvasElement?.getContext('2d');

        const speak = (message) => {
            if (!isVoiceEnabled) return;
            const now = Date.now();
            if (now - lastInstructionTimeRef.current > instructionDelay) {
                window.speechSynthesis.cancel();
                const utterance = new SpeechSynthesisUtterance(message);
                utterance.rate = 0.9;
                utterance.pitch = 1.1;
                window.speechSynthesis.speak(utterance);
                lastInstructionTimeRef.current = now;
            }
        };

        const updateInstruction = (text) => {
            setCurrentInstruction(text);
        };

        const captureScan = () => {
            const dataUrl = canvasElement.toDataURL('image/jpeg');
            const newCapture = {
                id: `${currentRegion.id}-${Date.now()}`,
                regionName: currentRegion.name,
                imageData: dataUrl,
                timestamp: new Date()
            };

            setCompletedScans(prev => [...prev, newCapture]);
            toast.success(`${currentRegion.name} captured!`, { icon: '✅' });

            const nextIndex = currentRegionIndex + 1;
            setCurrentRegionIndex(nextIndex);
            setScanProgress((nextIndex / regions.length) * 100);

            if (nextIndex < regions.length) {
                steadySinceRef.current = null;
                updateInstruction(regions[nextIndex].description);
                speak(`Scan successful. Now, show me your ${regions[nextIndex].name}.`);
            } else {
                updateInstruction("Scan complete! Well done.");
                speak("All scans are complete. Great job!");
                setIsComplete(true);
                cancelAnimationFrame(animationFrameId.current);

                // Auto-return to dashboard after 3 seconds
                setTimeout(() => {
                    handleComplete();
                }, 3000);
            }
        };

        const processVideoFrame = () => {
            if (!videoElement || !canvasElement || !ctx || isComplete) return;

            const { videoWidth, videoHeight } = videoElement;
            canvasElement.width = videoWidth;
            canvasElement.height = videoHeight;

            ctx.drawImage(videoElement, 0, 0, videoWidth, videoHeight);

            // Define bounding boxes for each region (same as original)
            const regionBoxes = {
                center: { x: videoWidth * 0.25, y: videoHeight * 0.3, width: videoWidth * 0.5, height: videoHeight * 0.4 },
                right: { x: videoWidth * 0.1, y: videoHeight * 0.3, width: videoWidth * 0.4, height: videoHeight * 0.4 },
                left: { x: videoWidth * 0.5, y: videoHeight * 0.3, width: videoWidth * 0.4, height: videoHeight * 0.4 },
                upper: { x: videoWidth * 0.2, y: videoHeight * 0.2, width: videoWidth * 0.6, height: videoHeight * 0.3 },
                lower: { x: videoWidth * 0.2, y: videoHeight * 0.5, width: videoWidth * 0.6, height: videoHeight * 0.3 }
            };

            const activeBox = regionBoxes[currentRegion.id];

            // Simple detection logic (same as original)
            const imageData = ctx.getImageData(activeBox.x, activeBox.y, activeBox.width, activeBox.height);
            const data = imageData.data;
            let brightnessSum = 0;
            let highContrastPixels = 0;

            for (let i = 0; i < data.length; i += 4) {
                const r = data[i];
                const g = data[i + 1];
                const b = data[i + 2];
                const brightness = (r + g + b) / 3;
                brightnessSum += brightness;
                if (brightness > 180) highContrastPixels++;
            }

            const avgBrightness = brightnessSum / (data.length / 4);
            const contrastRatio = highContrastPixels / (data.length / 4);
            detectionConfidenceRef.current = Math.min((avgBrightness / 255) * 0.7 + contrastRatio * 0.3, 1);

            // Draw bounding boxes (same as original)
            Object.entries(regionBoxes).forEach(([regionId, box]) => {
                const isCurrent = regionId === currentRegion.id;
                ctx.lineWidth = isCurrent ? 4 : 2;

                if (isCurrent) {
                    const progress = steadySinceRef.current ? (Date.now() - steadySinceRef.current) / scanDelay : 0;
                    const color = detectionConfidenceRef.current > confidenceThreshold ? `rgba(0, 255, 136, ${0.5 + detectionConfidenceRef.current * 0.5})` : `rgba(255, 255, 255, 0.7)`;
                    ctx.strokeStyle = color;
                    ctx.strokeRect(box.x, box.y, box.width, box.height);

                    // Draw progress bar
                    if(progress > 0) {
                        ctx.fillStyle = "#00ff88";
                        ctx.fillRect(box.x, box.y + box.height + 5, box.width * Math.min(progress, 1), 5);
                    }
                } else {
                    ctx.strokeStyle = "#ffffff50";
                    ctx.strokeRect(box.x, box.y, box.width, box.height);
                }
            });

            // Detection logic (same as original)
            if (detectionConfidenceRef.current > confidenceThreshold) {
                if (!steadySinceRef.current) {
                    steadySinceRef.current = Date.now();
                    updateInstruction("Perfect! Hold steady...");
                    speak("Perfect position, hold steady.");
                } else {
                    const elapsed = Date.now() - steadySinceRef.current;
                    if (elapsed >= scanDelay) {
                        captureScan();
                        return; // Exit early to prevent further processing
                    }
                }
            } else {
                if (steadySinceRef.current) {
                    updateInstruction(currentRegion.description);
                    speak("Please reposition.");
                }
                steadySinceRef.current = null;
            }

            animationFrameId.current = requestAnimationFrame(processVideoFrame);
        };



        if (videoElement && canvasElement && cameraStatus === "ready") {
            animationFrameId.current = requestAnimationFrame(processVideoFrame);
        }

        return () => {
            if (animationFrameId.current) {
                cancelAnimationFrame(animationFrameId.current);
            }
        };
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [isVoiceEnabled, currentRegionIndex, isComplete, cameraStatus]);

    // Separate effect for camera initialization
    useEffect(() => {
        const initializeCamera = async () => {
            try {
                // Check if mediaDevices is available
                if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
                    throw new Error("Camera not supported on this device");
                }

                // More permissive constraints for mobile devices
                const constraints = {
                    video: {
                        facingMode: { ideal: 'environment' }, // Prefer rear camera but allow fallback
                        width: { ideal: 1920, min: 640 },
                        height: { ideal: 1080, min: 480 },
                        aspectRatio: { ideal: 16/9 }
                    },
                    audio: false
                };

                console.log("Requesting camera access with constraints:", constraints);
                const stream = await navigator.mediaDevices.getUserMedia(constraints);
                streamRef.current = stream;

                console.log("Camera stream obtained successfully");

                // Enable flash/torch if available
                const track = stream.getVideoTracks()[0];
                console.log("Video track capabilities:", track.getCapabilities?.());

                if (track && 'applyConstraints' in track) {
                    try {
                        const capabilities = track.getCapabilities();
                        if (capabilities.torch) {
                            await track.applyConstraints({
                                advanced: [{ torch: flashEnabled }]
                            });
                            console.log("Flash enabled:", flashEnabled);
                        } else {
                            console.log("Flash not supported on this device");
                        }
                    } catch (torchError) {
                        console.log("Torch constraint failed:", torchError);
                        // Flash not supported, continue without it
                    }
                }

                const videoElement = videoRef.current;
                if (videoElement) {
                    videoElement.srcObject = stream;
                    videoElement.onloadedmetadata = () => {
                        console.log("Video metadata loaded");
                        videoElement.play().then(() => {
                            setCameraStatus("ready");
                            setCurrentInstruction(regions[0].description);
                            console.log("Camera ready and playing");
                        }).catch(playError => {
                            console.error("Video play failed:", playError);
                            setCameraStatus("error");
                            toast.error("Failed to start camera preview", { duration: 6000 });
                        });
                    };

                    videoElement.onerror = (error) => {
                        console.error("Video element error:", error);
                        setCameraStatus("error");
                        toast.error("Camera preview error", { duration: 6000 });
                    };
                }
            } catch (error) {
                console.error("Camera initialization failed:", error);
                setCameraStatus("error");

                let errorMessage = "Camera access failed. ";
                if (error.name === 'NotAllowedError') {
                    errorMessage += "Please allow camera permissions and refresh the page.";
                } else if (error.name === 'NotFoundError') {
                    errorMessage += "No camera found on this device.";
                } else if (error.name === 'NotSupportedError') {
                    errorMessage += "Camera not supported on this browser.";
                } else {
                    errorMessage += "Please check camera permissions and try again.";
                }

                toast.error(errorMessage, { duration: 8000 });
            }
        };

        if (cameraStatus === "initializing") {
            // Add a small delay to ensure DOM is ready
            setTimeout(initializeCamera, 100);
        }

        return () => {
            if (streamRef.current) {
                streamRef.current.getTracks().forEach(track => {
                    track.stop();
                    console.log("Camera track stopped");
                });
            }
        };
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [flashEnabled, cameraStatus]);

    const handleComplete = () => {
        if (streamRef.current) {
            streamRef.current.getTracks().forEach(track => track.stop());
        }
        onComplete(completedScans);
    };

    const handleExit = () => {
        if (streamRef.current) {
            streamRef.current.getTracks().forEach(track => track.stop());
        }
        onExit();
    };

    const requestCameraPermission = async () => {
        try {
            // First check if permissions API is available
            if ('permissions' in navigator) {
                const permission = await navigator.permissions.query({ name: 'camera' });
                console.log('Camera permission status:', permission.state);

                if (permission.state === 'denied') {
                    toast.error('Camera permission denied. Please enable camera access in your browser settings.', { duration: 8000 });
                    return false;
                }
            }

            // Try to get camera access
            const stream = await navigator.mediaDevices.getUserMedia({
                video: { facingMode: 'environment' }
            });

            // Stop the test stream immediately
            stream.getTracks().forEach(track => track.stop());

            toast.success('Camera permission granted!', { duration: 3000 });
            return true;
        } catch (error) {
            console.error('Permission request failed:', error);
            toast.error('Failed to get camera permission. Please check your browser settings.', { duration: 6000 });
            return false;
        }
    };

    const restartScan = async () => {
        // Reset all scanning state
        setCompletedScans([]);
        setScanProgress(0);
        setIsComplete(false);
        setCurrentRegionIndex(0);
        setCurrentInstruction("Getting camera ready...");

        // Reset refs
        lastInstructionTimeRef.current = 0;
        detectionConfidenceRef.current = 0;
        steadySinceRef.current = null;

        // Cancel any ongoing animation frame
        if (animationFrameId.current) {
            cancelAnimationFrame(animationFrameId.current);
        }

        // Stop existing stream
        if (streamRef.current) {
            streamRef.current.getTracks().forEach(track => track.stop());
            streamRef.current = null;
        }

        // Request permission first if we're in error state
        if (cameraStatus === 'error') {
            const hasPermission = await requestCameraPermission();
            if (!hasPermission) {
                return; // Don't proceed if permission denied
            }
        }

        setCameraStatus("initializing");
    };

    // Show orientation prompt if not in landscape
    if (!isLandscape) {
        return (
            <div className="mobile-scanner orientation-prompt">
                <motion.div
                    className="orientation-content"
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.6 }}
                >
                    <div className="orientation-icon">📱➡️📱</div>
                    <h2>Rotate Your Device</h2>
                    <p>Please rotate your device to landscape mode for the best scanning experience</p>
                    <button onClick={handleExit} className="skip-btn">
                        Continue Anyway
                    </button>
                </motion.div>
            </div>
        );
    }

    if (isComplete) {
        return (
            <div className="mobile-scanner completion-screen">
                <motion.div
                    className="completion-content"
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.6 }}
                >
                    <div className="completion-icon">✅</div>
                    <h2>Scan Complete!</h2>
                    <p>All {completedScans.length} scans captured successfully</p>
                    <p className="auto-return">Returning to dashboard...</p>
                </motion.div>
            </div>
        );
    }

    return (
        <div className="mobile-scanner">
            {/* Header Controls */}
            <div className="scanner-header">
                <motion.button 
                    className="exit-btn"
                    onClick={handleExit}
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                >
                    <X size={24} />
                </motion.button>
                
                <div className="header-controls">
                    <motion.button 
                        className="control-btn"
                        onClick={() => setIsVoiceEnabled(!isVoiceEnabled)}
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.9 }}
                    >
                        {isVoiceEnabled ? <Mic size={20} /> : <MicOff size={20} />}
                    </motion.button>
                    
                    <motion.button 
                        className="control-btn"
                        onClick={restartScan}
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.9 }}
                    >
                        <RotateCcw size={20} />
                    </motion.button>
                </div>
            </div>

            {/* Camera View */}
            <div className="camera-container">
                <video 
                    ref={videoRef} 
                    autoPlay 
                    playsInline 
                    muted
                    className="camera-video"
                />
                <canvas
                    ref={canvasRef}
                    className="camera-canvas"
                />
                
                {/* Instruction Overlay */}
                <div className="instruction-overlay">
                    <motion.div 
                        className="instruction-content"
                        key={currentInstruction}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.5 }}
                    >
                        <h2>{currentInstruction}</h2>
                        <div className="progress-info">
                            <span>Scan {currentRegionIndex + 1} of 5</span>
                            <div className="progress-bar">
                                <motion.div 
                                    className="progress-fill"
                                    initial={{ width: 0 }}
                                    animate={{ width: `${scanProgress}%` }}
                                    transition={{ duration: 0.5 }}
                                />
                            </div>
                        </div>
                    </motion.div>
                </div>

                {/* Camera Error */}
                {cameraStatus === 'error' && (
                    <div className="camera-error">
                        <h3>Camera Access Required</h3>
                        <p>This app needs camera access to scan your teeth. Please:</p>
                        <ul className="error-steps">
                            <li>Allow camera permissions when prompted</li>
                            <li>Make sure no other app is using the camera</li>
                            <li>Try refreshing the page if needed</li>
                        </ul>
                        <div className="error-buttons">
                            <button onClick={restartScan} className="retry-btn">
                                Try Again
                            </button>
                            <button onClick={handleExit} className="exit-error-btn">
                                Go Back
                            </button>
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
};

export default MobileScanner;
