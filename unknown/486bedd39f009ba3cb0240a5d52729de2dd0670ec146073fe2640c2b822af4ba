import React, { useRef, useEffect, useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { <PERSON>, Mic, MicOff, RotateCcw, Download, Settings, CheckCircle, AlertCircle } from "lucide-react";
import toast from "react-hot-toast";
import "./DentalScanner.css";

const DentalScanner = () => {
    const videoRef = useRef(null);
    const canvasRef = useRef(null);
    const animationFrameId = useRef(null);

    const [isVoiceEnabled, setIsVoiceEnabled] = useState(true);
    const [cameraStatus, setCameraStatus] = useState("initializing");
    const [showSettings, setShowSettings] = useState(false);
    const [scanHistory, setScanHistory] = useState([]);
    const [currentInstruction, setCurrentInstruction] = useState("Getting camera ready...");
    const [scanProgress, setScanProgress] = useState(0);

    useEffect(() => {
        const regions = [
            { id: "center", name: "Center", description: "Front teeth alignment" },
            { id: "right", name: "Right Side", description: "Right molar alignment" },
            { id: "left", name: "Left Side", description: "Left molar alignment" },
            { id: "upper", name: "Upper Arch", description: "Upper teeth overview" },
            { id: "lower", name: "Lower Arch", description: "Lower teeth overview" }
        ];
        const instructionDelay = 3000;
        const scanDelay = 1500; // Time to hold steady for a scan
        const confidenceThreshold = 0.6; // Required confidence for capture
        
        let regionIndex = 0;
        let currentRegion = regions[regionIndex];
        let isScanning = false;
        let lastInstructionTime = 0;
        let detectionConfidence = 0;
        let steadySince = null; // Timestamp when user starts holding steady

        const videoElement = videoRef.current;
        const canvasElement = canvasRef.current;
        const ctx = canvasElement.getContext('2d');

        const speak = (message) => {
            if (!isVoiceEnabled) return;
            const now = Date.now();
            if (now - lastInstructionTime > instructionDelay) {
                window.speechSynthesis.cancel(); // Cancel previous guidance
                const utterance = new SpeechSynthesisUtterance(message);
                utterance.rate = 0.9;
                utterance.pitch = 1.1;
                window.speechSynthesis.speak(utterance);
                lastInstructionTime = now;
            }
        };
        
        const updateInstruction = (text) => {
            setCurrentInstruction(text);
        };

        const captureScan = () => {
            const dataUrl = canvasElement.toDataURL('image/jpeg');
            const newCapture = { 
                id: `${currentRegion.id}-${Date.now()}`, 
                regionName: currentRegion.name, 
                imageData: dataUrl,
                timestamp: new Date()
            };
            setScanHistory(prev => [...prev, newCapture]);
            toast.success(`${currentRegion.name} captured!`, { icon: '✅' });
            
            // Move to next region
            regionIndex++;
            setScanProgress((regionIndex / regions.length) * 100);

            if (regionIndex < regions.length) {
                currentRegion = regions[regionIndex];
                isScanning = false;
                steadySince = null;
                updateInstruction(`Next: ${currentRegion.description}.`);
                speak(`Scan successful. Now, show me your ${currentRegion.name}.`);
            } else {
                updateInstruction("Scan complete! Well done.");
                speak("All scans are complete. Great job!");
                // Stop the loop? Or allow re-scans? For now, we stop.
                cancelAnimationFrame(animationFrameId.current);
            }
        };

        const processVideoFrame = () => {
            if (!videoElement || !canvasElement || videoElement.readyState < 2 || regionIndex >= regions.length) {
                animationFrameId.current = requestAnimationFrame(processVideoFrame);
                return;
            }

            canvasElement.width = videoElement.videoWidth;
            canvasElement.height = videoElement.videoHeight;
            ctx.drawImage(videoElement, 0, 0, canvasElement.width, canvasElement.height);

            const centerX = canvasElement.width / 2;
            const centerY = canvasElement.height / 2;
            
            const regionBoxes = {
                center: { x: centerX - 100, y: centerY - 50, width: 200, height: 100 },
                right: { x: centerX - 180, y: centerY - 50, width: 120, height: 100 },
                left: { x: centerX + 60, y: centerY - 50, width: 120, height: 100 },
                upper: { x: centerX - 120, y: centerY - 100, width: 240, height: 80 },
                lower: { x: centerX - 120, y: centerY + 20, width: 240, height: 80 }
            };

            const activeBox = regionBoxes[currentRegion.id];

            // --- Simple Detection Logic ---
            const imageData = ctx.getImageData(activeBox.x, activeBox.y, activeBox.width, activeBox.height);
            const data = imageData.data;
            let brightnessSum = 0;
            let highContrastPixels = 0;
            for (let i = 0; i < data.length; i += 4) {
                const r = data[i];
                const g = data[i + 1];
                const b = data[i + 2];
                const brightness = (r + g + b) / 3;
                brightnessSum += brightness;
                // Simple contrast check (white-ish pixels, potential teeth reflection)
                if (brightness > 180) highContrastPixels++;
            }
            const avgBrightness = brightnessSum / (data.length / 4);
            const contrastScore = highContrastPixels / (data.length / 4);
            
            // Confidence is a mix of brightness and contrast
            detectionConfidence = (avgBrightness / 255) * 0.4 + contrastScore * 0.6;


            // --- Drawing and Guidance ---
            Object.entries(regionBoxes).forEach(([regionId, box]) => {
                const isCurrent = regionId === currentRegion.id;
                ctx.lineWidth = isCurrent ? 4 : 2;
                
                if (isCurrent) {
                    const progress = steadySince ? (Date.now() - steadySince) / scanDelay : 0;
                    const color = detectionConfidence > confidenceThreshold ? `rgba(0, 255, 136, ${0.5 + detectionConfidence * 0.5})` : `rgba(255, 255, 255, 0.7)`;
                    ctx.strokeStyle = color;
                    ctx.strokeRect(box.x, box.y, box.width, box.height);

                    // Draw progress bar
                    if(progress > 0) {
                        ctx.fillStyle = "#00ff88";
                        ctx.fillRect(box.x, box.y + box.height + 5, box.width * Math.min(progress, 1), 5);
                    }

                } else {
                    ctx.strokeStyle = "#ffffff50";
                    ctx.strokeRect(box.x, box.y, box.width, box.height);
                }
            });

            // --- State Machine Logic ---
            if (detectionConfidence > confidenceThreshold) {
                if (!steadySince) {
                    steadySince = Date.now();
                    updateInstruction("Hold it right there...");
                    speak("Perfect, hold steady.");
                } else {
                    const elapsed = Date.now() - steadySince;
                    if (elapsed >= scanDelay) {
                        captureScan();
                    }
                }
            } else {
                if (steadySince) {
                    updateInstruction(`Align your ${currentRegion.name}.`);
                    speak("Lost it, please reposition.");
                } else {
                     updateInstruction(`Position your ${currentRegion.name} in the box.`);
                }
                steadySince = null; // Reset if confidence drops
            }
            
            animationFrameId.current = requestAnimationFrame(processVideoFrame);
        };

        const initializeCamera = async () => {
            try {
                const stream = await navigator.mediaDevices.getUserMedia({
                    video: { width: { ideal: 1280 }, height: { ideal: 720 }, facingMode: 'user' }
                });
                if (videoElement) {
                    videoElement.srcObject = stream;
                    videoElement.onplay = () => {
                        setCameraStatus("ready");
                        updateInstruction(`Scan 1: ${regions[0].description}.`);
                        speak("Camera ready. Let's begin. Please focus on your front teeth.", "high");
                        animationFrameId.current = requestAnimationFrame(processVideoFrame);
                    };
                }
            } catch (error) {
                console.error("Failed to access camera:", error);
                setCameraStatus("error");
                toast.error("Camera access failed. Please check permissions and refresh.", { duration: 6000 });
            }
        };

        initializeCamera();

        return () => {
            cancelAnimationFrame(animationFrameId.current);
            if (videoElement && videoElement.srcObject) {
                videoElement.srcObject.getTracks().forEach(track => track.stop());
            }
        };
    }, [isVoiceEnabled]); // Only re-run if voice is toggled

    const retryCamera = () => window.location.reload();

    const restartScan = () => {
        // This is a bit of a hack. A better way would be to manage state more robustly.
        window.location.reload();
    };

    return (
        <div className="dental-scanner">
            <div className="scanner-header">
                <div className="status-indicators">
                    <div className={`status-indicator ${cameraStatus}`}>
                        <Camera size={20} />
                        <span>{cameraStatus}</span>
                    </div>
                    <div className="status-indicator">
                        {isVoiceEnabled ? <Mic size={20} /> : <MicOff size={20} />}
                        <span>Voice {isVoiceEnabled ? 'On' : 'Off'}</span>
                    </div>
                </div>
                <div className="scanner-controls">
                    <motion.button whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }} onClick={restartScan} className="control-btn" title="Restart Scan">
                        <RotateCcw size={20} />
                    </motion.button>
                    <motion.button whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }} onClick={() => setIsVoiceEnabled(v => !v)} className="control-btn" title="Toggle Voice">
                        {isVoiceEnabled ? <Mic size={20} /> : <MicOff size={20} />}
                    </motion.button>
                    <motion.button whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }} onClick={() => setShowSettings(s => !s)} className="control-btn" title="Settings">
                        <Settings size={20} />
                    </motion.button>
                </div>
            </div>

            <div className="scanner-main">
                <div className="camera-section">
                    <div className="camera-container">
                        <video 
                            ref={videoRef} 
                            autoPlay 
                            playsInline 
                            muted
                            style={{ width: "100%", borderRadius: "15px", display: 'block' }}
                        />
                        <canvas
                            ref={canvasRef}
                            className="camera--canvas"
                            style={{ position: "absolute", top: 0, left: 0, width: "100%", height: "100%", pointerEvents: "none" }}
                        />
                        <div className="scan-overlay-info">
                            <h3>{currentInstruction}</h3>
                            <div className="scan-progress-bar">
                                <motion.div 
                                    className="scan-progress-bar-inner"
                                    initial={{ width: 0 }}
                                    animate={{ width: `${scanProgress}%`}}
                                    transition={{ ease: "easeInOut", duration: 0.5 }}
                                />
                            </div>
                        </div>
                        {cameraStatus === 'error' && (
                            <div className="camera-error">
                                <AlertCircle size={48} />
                                <h3>Camera Access Failed</h3>
                                <p>Please allow camera permissions and refresh the page.</p>
                                <button onClick={retryCamera}>Retry</button>
                            </div>
                        )}
                    </div>
                </div>
                <div className="info-panel">
                    <h4>Scan History</h4>
                    {scanHistory.length === 0 ? (
                        <p className="no-scans">No scans captured yet.</p>
                    ) : (
                        <div className="scan-history-list">
                            {scanHistory.map((scan) => (
                                <motion.div 
                                    key={scan.id} 
                                    className="scan-history-item"
                                    initial={{ opacity: 0, y: -20 }}
                                    animate={{ opacity: 1, y: 0 }}
                                >
                                    <img src={scan.imageData} alt={`Scan of ${scan.regionName}`} />
                                    <div className="scan-item-info">
                                        <strong>{scan.regionName}</strong>
                                        <p>{scan.timestamp.toLocaleTimeString()}</p>
                                    </div>
                                    <a href={scan.imageData} download={`${scan.regionName}_scan.jpg`}><Download size={18}/></a>
                                </motion.div>
                            ))}
                        </div>
                    )}
                </div>
            </div>
            
            <AnimatePresence>
                {showSettings && (
                    <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} exit={{ opacity: 0, y: 20 }} className="settings-panel">
                        <h3>Settings</h3>
                        <div className="setting-item">
                        <label>
                            <input type="checkbox" checked={isVoiceEnabled} onChange={(e) => setIsVoiceEnabled(e.target.checked)} />
                            Voice Guidance
                        </label>
                        </div>
                        <div className="setting-item">
                        <button onClick={restartScan} className="retry-camera-btn">
                            Restart Full Scan
                        </button>
                        </div>
                    </motion.div>
                )}
            </AnimatePresence>
        </div>
    );
};

export default DentalScanner;