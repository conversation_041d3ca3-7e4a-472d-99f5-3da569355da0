/* Dental Scanner Component Styles */
.dental-scanner {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

/* Scanner Header */
.scanner-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid rgba(102, 126, 234, 0.1);
}

.status-indicators {
  display: flex;
  gap: 1.5rem;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 25px;
  font-size: 0.9rem;
  font-weight: 600;
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.status-indicator.ready {
  color: #27ae60;
  background: rgba(39, 174, 96, 0.1);
  border-color: rgba(39, 174, 96, 0.3);
}

.status-indicator.initializing {
  color: #f39c12;
  background: rgba(243, 156, 18, 0.1);
  border-color: rgba(243, 156, 18, 0.3);
}

.status-indicator.error {
  color: #e74c3c;
  background: rgba(231, 76, 60, 0.1);
  border-color: rgba(231, 76, 60, 0.3);
}

.scanner-controls {
  display: flex;
  gap: 0.5rem;
}

.control-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  border: none;
  border-radius: 12px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.control-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.control-btn.reset {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
  box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
}

.control-btn.reset:hover {
  box-shadow: 0 6px 20px rgba(231, 76, 60, 0.4);
}

/* Scanner Main Content */
.scanner-main {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 2rem;
  margin-bottom: 2rem;
}

/* Camera Section */
.camera-section {
  position: relative;
}

.camera-container {
  position: relative;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  background: #000;
}

.camera--canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.scan-overlay-info {
  position: absolute;
  bottom: 20px;
  left: 20px;
  right: 20px;
  background: rgba(0, 0, 0, 0.6);
  color: white;
  padding: 1rem;
  border-radius: 10px;
  text-align: center;
  backdrop-filter: blur(5px);
  z-index: 10;
}

.scan-overlay-info h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1.1rem;
  font-weight: 600;
}

.scan-progress-bar {
  width: 100%;
  height: 8px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 4px;
  overflow: hidden;
}

.scan-progress-bar-inner {
  height: 100%;
  background: linear-gradient(90deg, #667eea, #764ba2);
  border-radius: 4px;
}

.camera-canvas {
  width: 100%;
  height: auto;
  display: block;
  border-radius: 15px;
}

.camera-error {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: white;
  background: rgba(0, 0, 0, 0.8);
  padding: 2rem;
  border-radius: 15px;
  backdrop-filter: blur(10px);
}

.camera-error h3 {
  margin: 1rem 0;
  color: #e74c3c;
}

.camera-error button {
  margin-top: 1rem;
  padding: 0.75rem 1.5rem;
  background: #667eea;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
}

/* Info Panel */
.info-panel {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  background: #fdfdff;
  padding: 1.5rem;
  border-radius: 15px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 0, 0, 0.05);
  max-height: 600px;
  overflow-y: auto;
}

.info-panel h4 {
  margin: 0;
  font-size: 1.25rem;
  color: #333;
  border-bottom: 2px solid #f0f0f0;
  padding-bottom: 0.75rem;
}

.no-scans {
  text-align: center;
  margin-top: 2rem;
  color: #888;
}

.scan-history-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-top: 0.5rem;
}

.scan-history-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.75rem;
  background: white;
  border: 1px solid #e9e9e9;
  border-radius: 10px;
  transition: all 0.2s ease-in-out;
}

.scan-history-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.07);
  border-color: #667eea;
}

.scan-history-item img {
  width: 80px;
  height: 60px;
  border-radius: 8px;
  object-fit: cover;
}

.scan-item-info {
  flex-grow: 1;
}

.scan-item-info strong {
  display: block;
  font-size: 1rem;
  color: #2c3e50;
  margin-bottom: 0.25rem;
}

.scan-item-info p {
  margin: 0;
  font-size: 0.8rem;
  color: #7f8c8d;
}

.scan-history-item a {
  color: #667eea;
  padding: 0.5rem;
  border-radius: 8px;
  background-color: #f0f2ff;
}

.scan-history-item a:hover {
  background-color: #e0e4ff;
}

/* Cleanup old styles that are no longer used */
.current-region, .scan-stats, .session-info, .scan-history {
  display: none;
}

/* Scan Stats */
.scan-stats {
  background: white;
  padding: 1.5rem;
  border-radius: 15px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.stat-item:last-child {
  border-bottom: none;
}

.stat-label {
  font-weight: 600;
  color: #666;
}

.stat-value {
  font-weight: 700;
  font-size: 1.1rem;
  color: #2c3e50;
}

.quality-excellent {
  color: #27ae60;
}

.quality-good {
  color: #3498db;
}

.quality-fair {
  color: #f39c12;
}

.quality-poor {
  color: #e74c3c;
}

/* Session Info */
.session-info {
  background: white;
  padding: 1.5rem;
  border-radius: 15px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.session-info h4 {
  margin: 0 0 1rem 0;
  color: #2c3e50;
  font-size: 1.1rem;
}

.session-info p {
  margin: 0.5rem 0;
  font-size: 0.9rem;
  color: #666;
}

.session-info strong {
  color: #2c3e50;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 1rem;
  border: none;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.95rem;
}

.action-btn.export {
  background: linear-gradient(135deg, #27ae60, #2ecc71);
  color: white;
  box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
}

.action-btn.export:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(39, 174, 96, 0.4);
}

.action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
}

/* Settings Panel */
.settings-panel {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: white;
  padding: 2rem;
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  z-index: 100;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  min-width: 300px;
}

.settings-panel h3 {
  margin: 0 0 1.5rem 0;
  padding-bottom: 1rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
}

.setting-item {
  margin-bottom: 1rem;
}

.setting-item label {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: white;
  cursor: pointer;
}

.setting-item input[type="checkbox"] {
  width: 20px;
  height: 20px;
  accent-color: #764ba2;
}

.setting-item input[type="number"] {
  width: 80px;
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 8px;
  margin-left: 1rem;
}

.setting-item select {
  width: 200px;
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 8px;
  margin-left: 1rem;
  background: white;
  font-size: 0.9rem;
}

.retry-camera-btn {
  width: 100%;
  padding: 0.75rem;
  border: none;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.9);
  color: #667eea;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s;
}

.retry-camera-btn:hover {
  background: white;
  color: #764ba2;
}

/* Scan History */
.scan-history {
  background: white;
  padding: 1.5rem;
  border-radius: 15px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.scan-history h3 {
  margin: 0 0 1rem 0;
  color: #2c3e50;
  font-size: 1.2rem;
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.history-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.75rem;
  background: rgba(102, 126, 234, 0.05);
  border-radius: 10px;
  border-left: 4px solid #667eea;
}

.history-icon {
  color: #27ae60;
}

.history-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.history-region {
  font-weight: 600;
  color: #2c3e50;
  font-size: 0.9rem;
}

.history-time {
  font-size: 0.8rem;
  color: #666;
}

.history-quality {
  font-size: 0.8rem;
  font-weight: 600;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  background: rgba(39, 174, 96, 0.1);
  color: #27ae60;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .scanner-main {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .info-panel {
    grid-row: 2;
    max-height: 400px;
  }
}

@media (max-width: 768px) {
  .dental-scanner {
    padding: 1rem;
    margin: 0 1rem;
  }
  
  .scanner-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }
  
  .status-indicators {
    justify-content: space-around;
  }
  
  .scanner-controls {
    justify-content: space-around;
  }
  
  .info-panel {
    padding: 1rem;
  }
  
  .region-name {
    font-size: 1.2rem;
  }
  
  .settings-panel {
    margin: 1rem;
    min-width: auto;
    width: calc(100% - 2rem);
  }
}

@media (max-width: 480px) {
  .status-indicators {
    flex-wrap: wrap;
    gap: 0.5rem;
  }
  
  .scanner-controls {
    gap: 0.5rem;
  }
  
  .control-btn {
    width: 44px;
    height: 44px;
    border-radius: 10px;
  }
  
  .scan-history-item {
    flex-direction: column;
    align-items: flex-start;
  }
} 