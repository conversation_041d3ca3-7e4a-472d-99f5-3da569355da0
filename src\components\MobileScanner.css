/* Mobile Scanner Full-Screen Styles */
.mobile-scanner {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: #000;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  /* Force landscape orientation */
  transform-origin: center center;
}

/* Orientation Prompt */
.orientation-prompt {
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.orientation-content {
  text-align: center;
  color: white;
  padding: 2rem;
  max-width: 300px;
}

.orientation-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
  animation: rotate-hint 2s ease-in-out infinite;
}

@keyframes rotate-hint {
  0%, 100% { transform: rotate(0deg); }
  50% { transform: rotate(90deg); }
}

.orientation-content h2 {
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0 0 1rem 0;
  color: white;
}

.orientation-content p {
  font-size: 1rem;
  margin: 0 0 2rem 0;
  opacity: 0.9;
  line-height: 1.5;
}

.skip-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  padding: 0.75rem 1.5rem;
  color: white;
  font-family: inherit;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.skip-btn:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* Scanner Header */
.scanner-header {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1001;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: linear-gradient(180deg, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0.4) 50%, transparent 100%);
}

.exit-btn {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  border-radius: 50%;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  cursor: pointer;
  backdrop-filter: blur(10px);
  transition: all 0.2s ease;
}

.exit-btn:hover {
  background: rgba(255, 255, 255, 0.3);
}

.header-controls {
  display: flex;
  gap: 0.75rem;
}

.control-btn {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  border-radius: 50%;
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  cursor: pointer;
  backdrop-filter: blur(10px);
  transition: all 0.2s ease;
}

.control-btn:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* Camera Container */
.camera-container {
  position: relative;
  flex: 1;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.camera-video {
  width: 100%;
  height: 100%;
  object-fit: cover;
  /* No mirroring for rear camera */
}

.camera-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  /* No mirroring for rear camera */
}

/* Instruction Overlay */
.instruction-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(0deg, rgba(0,0,0,0.9) 0%, rgba(0,0,0,0.6) 50%, transparent 100%);
  padding: 2rem 1.5rem 3rem;
  color: white;
  text-align: center;
}

.instruction-content h2 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 1rem 0;
  color: white;
  text-shadow: 0 2px 4px rgba(0,0,0,0.5);
}

.progress-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.75rem;
}

.progress-info span {
  font-size: 0.9rem;
  opacity: 0.9;
  font-weight: 500;
}

.progress-bar {
  width: 100%;
  max-width: 200px;
  height: 4px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #00ff88 0%, #00cc6a 100%);
  border-radius: 2px;
  transition: width 0.5s ease;
}

/* Camera Error */
.camera-error {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: white;
  background: rgba(0, 0, 0, 0.9);
  padding: 2rem;
  border-radius: 16px;
  backdrop-filter: blur(10px);
  max-width: 90%;
  width: 400px;
}

.camera-error h3 {
  font-size: 1.25rem;
  margin: 0 0 1rem 0;
  color: #ff6b6b;
}

.camera-error p {
  font-size: 1rem;
  margin: 0 0 1rem 0;
  opacity: 0.9;
}

.error-steps {
  text-align: left;
  margin: 1rem 0 2rem 0;
  padding-left: 1rem;
}

.error-steps li {
  margin: 0.5rem 0;
  opacity: 0.8;
  font-size: 0.9rem;
}

.error-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.retry-btn {
  background: #00ff88;
  border: none;
  border-radius: 8px;
  padding: 0.75rem 1.5rem;
  color: white;
  font-family: inherit;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  flex: 1;
  min-width: 120px;
}

.retry-btn:hover {
  background: #00cc6a;
}

.exit-error-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  padding: 0.75rem 1.5rem;
  color: white;
  font-family: inherit;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  flex: 1;
  min-width: 120px;
}

.exit-error-btn:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* Completion Screen */
.completion-screen {
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.completion-content {
  text-align: center;
  color: white;
  padding: 2rem;
}

.completion-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.completion-content h2 {
  font-size: 2rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  color: white;
}

.completion-content p {
  font-size: 1.1rem;
  margin: 0 0 0.5rem 0;
  opacity: 0.9;
}

.auto-return {
  font-size: 0.9rem !important;
  opacity: 0.7 !important;
  font-style: italic;
}

/* Safe Area Support for Notched Devices */
@supports (padding: max(0px)) {
  .scanner-header {
    padding-top: max(1rem, env(safe-area-inset-top));
    padding-left: max(1rem, env(safe-area-inset-left));
    padding-right: max(1rem, env(safe-area-inset-right));
  }
  
  .instruction-overlay {
    padding-bottom: max(3rem, env(safe-area-inset-bottom) + 2rem);
    padding-left: max(1.5rem, env(safe-area-inset-left));
    padding-right: max(1.5rem, env(safe-area-inset-right));
  }
}

/* Landscape Orientation */
@media (orientation: landscape) {
  .instruction-overlay {
    padding: 1rem 1.5rem 2rem;
  }
  
  .instruction-content h2 {
    font-size: 1.1rem;
  }
  
  .progress-info {
    gap: 0.5rem;
  }
  
  .scanner-header {
    padding: 0.75rem 1rem;
  }
}

/* High DPI Displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .camera-canvas {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .mobile-scanner {
    background: #000;
  }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  .completion-content,
  .instruction-content,
  .control-btn,
  .exit-btn {
    transition: none;
  }
  
  .progress-fill {
    transition: none;
  }
}
