import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Play, Pause, CheckCircle } from 'lucide-react';

const DemoGuide = ({ onClose }) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);

  const steps = [
    {
      title: "Welcome to AlignSmart",
      description: "Professional dental monitoring for aligner treatment tracking",
      icon: "🦷",
      details: [
        "Advanced teeth detection technology",
        "Real-time quality assessment",
        "Professional voice guidance",
        "Complete session management"
      ]
    },
    {
      title: "Camera Setup",
      description: "Position yourself for optimal scanning",
      icon: "📷",
      details: [
        "Ensure good lighting conditions",
        "Position face close to camera",
        "Keep teeth clearly visible",
        "Follow voice instructions"
      ]
    },
    {
      title: "Scanning Process",
      description: "Systematic coverage of all dental regions",
      icon: "🔍",
      details: [
        "Center: Front teeth alignment",
        "Right/Left: Molar alignment",
        "Upper/Lower: Arch overview",
        "Automatic quality assessment"
      ]
    },
    {
      title: "Quality Indicators",
      description: "Real-time feedback on scan quality",
      icon: "📊",
      details: [
        "Excellent (80%+): Optimal quality",
        "Good (60-79%): Acceptable quality",
        "Fair (40-59%): Needs adjustment",
        "Poor (<40%): Reposition required"
      ]
    },
    {
      title: "Data Management",
      description: "Export and track your progress",
      icon: "💾",
      details: [
        "High-quality image captures",
        "Session data export",
        "Complete scan history",
        "Professional metadata"
      ]
    }
  ];

  const startDemo = () => {
    setIsPlaying(true);
    const interval = setInterval(() => {
      setCurrentStep(prev => {
        if (prev >= steps.length - 1) {
          clearInterval(interval);
          setIsPlaying(false);
          return prev;
        }
        return prev + 1;
      });
    }, 3000);
  };

  const pauseDemo = () => {
    setIsPlaying(false);
  };

  const nextStep = () => {
    setCurrentStep(prev => Math.min(prev + 1, steps.length - 1));
  };

  const prevStep = () => {
    setCurrentStep(prev => Math.max(prev - 1, 0));
  };

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.9 }}
      className="demo-guide"
    >
      <div className="demo-header">
        <h2>AlignSmart Demo Guide</h2>
        <button onClick={onClose} className="close-btn">×</button>
      </div>

      <div className="demo-content">
        <div className="step-display">
          <div className="step-icon">{steps[currentStep].icon}</div>
          <h3>{steps[currentStep].title}</h3>
          <p>{steps[currentStep].description}</p>
          
          <div className="step-details">
            {steps[currentStep].details.map((detail, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
                className="detail-item"
              >
                <CheckCircle size={16} />
                <span>{detail}</span>
              </motion.div>
            ))}
          </div>
        </div>

        <div className="demo-controls">
          <div className="step-indicators">
            {steps.map((_, index) => (
              <div
                key={index}
                className={`step-dot ${index === currentStep ? 'active' : ''}`}
                onClick={() => setCurrentStep(index)}
              />
            ))}
          </div>

          <div className="control-buttons">
            <button onClick={prevStep} disabled={currentStep === 0}>
              ←
            </button>
            
            {isPlaying ? (
              <button onClick={pauseDemo} className="play-btn">
                <Pause size={20} />
              </button>
            ) : (
              <button onClick={startDemo} className="play-btn">
                <Play size={20} />
              </button>
            )}
            
            <button onClick={nextStep} disabled={currentStep === steps.length - 1}>
              →
            </button>
          </div>
        </div>
      </div>

      <div className="demo-footer">
        <div className="progress-bar">
          <div 
            className="progress-fill" 
            style={{ width: `${((currentStep + 1) / steps.length) * 100}%` }}
          />
        </div>
        <span className="progress-text">
          Step {currentStep + 1} of {steps.length}
        </span>
      </div>
    </motion.div>
  );
};

export default DemoGuide; 