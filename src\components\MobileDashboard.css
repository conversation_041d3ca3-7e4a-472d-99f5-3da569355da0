/* Mobile Dashboard Styles */
.mobile-dashboard {
  padding: 1rem;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  overflow-y: auto;
}

/* Welcome Section */
.welcome-section {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.welcome-content h2 {
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  color: white;
}

.welcome-content p {
  font-size: 1rem;
  margin: 0 0 1rem 0;
  opacity: 0.9;
}

.patient-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  opacity: 0.8;
}

/* Stats Grid */
.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.stat-card {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 1rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.stat-icon {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  padding: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.stat-content h3 {
  font-size: 1.25rem;
  font-weight: 700;
  margin: 0;
  color: white;
}

.stat-content p {
  font-size: 0.8rem;
  margin: 0;
  opacity: 0.8;
}

/* Main Action Button */
.main-action {
  margin-bottom: 2rem;
}

.start-scan-btn {
  width: 100%;
  background: linear-gradient(135deg, #00ff88 0%, #00cc6a 100%);
  border: none;
  border-radius: 16px;
  padding: 1.5rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  color: white;
  font-family: inherit;
  cursor: pointer;
  box-shadow: 0 8px 32px rgba(0, 255, 136, 0.3);
  transition: all 0.3s ease;
}

.start-scan-btn:active {
  transform: scale(0.98);
}

.btn-icon {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 0.75rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-content h3 {
  font-size: 1.25rem;
  font-weight: 700;
  margin: 0 0 0.25rem 0;
  text-align: left;
}

.btn-content p {
  font-size: 0.9rem;
  margin: 0;
  opacity: 0.9;
  text-align: left;
}

/* Recent Scans */
.recent-scans {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.recent-scans h3 {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0 0 1rem 0;
  color: white;
}

.scan-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.scan-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 0.75rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.scan-thumbnail {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  overflow: hidden;
  flex-shrink: 0;
}

.scan-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.scan-details {
  flex: 1;
}

.scan-details h4 {
  font-size: 0.9rem;
  font-weight: 600;
  margin: 0 0 0.25rem 0;
  color: white;
}

.scan-details p {
  font-size: 0.8rem;
  margin: 0;
  opacity: 0.7;
}

.download-btn {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  border-radius: 8px;
  padding: 0.5rem;
  color: white;
  text-decoration: none;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.download-btn:hover {
  background: rgba(255, 255, 255, 0.3);
}

.view-all-btn {
  width: 100%;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  padding: 0.75rem;
  color: white;
  font-family: inherit;
  font-size: 0.9rem;
  cursor: pointer;
  margin-top: 0.75rem;
  transition: all 0.2s ease;
}

.view-all-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

/* Quick Actions */
.quick-actions {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 1.5rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.quick-actions h3 {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0 0 1rem 0;
  color: white;
}

.action-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.75rem;
}

.action-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 1rem;
  color: white;
  font-family: inherit;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.2s ease;
}

.action-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.action-btn span {
  font-size: 0.8rem;
  font-weight: 500;
}

/* Responsive adjustments */
@media (max-width: 480px) {
  .mobile-dashboard {
    padding: 0.75rem;
  }
  
  .stats-grid {
    gap: 0.75rem;
  }
  
  .stat-card {
    padding: 0.75rem;
  }
  
  .start-scan-btn {
    padding: 1.25rem;
  }
  
  .btn-content h3 {
    font-size: 1.1rem;
  }
  
  .btn-content p {
    font-size: 0.85rem;
  }
}
