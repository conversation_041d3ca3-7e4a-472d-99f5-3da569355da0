import React, { useState } from 'react';
import { Toaster } from 'react-hot-toast';
import { HelpCircle } from 'lucide-react';
import DentalScanner from './components/DentalScanner';
import DemoGuide from './components/DemoGuide';
import './App.css';

function App() {
  const [showDemo, setShowDemo] = useState(false);

  return (
    <div className="App">
      <Toaster 
        position="top-right"
        toastOptions={{
          duration: 4000,
          style: {
            background: '#363636',
            color: '#fff',
          },
        }}
      />
      <header className="app-header">
        <div className="header-content">
          <div className="logo-section">
            <div className="logo-icon">🦷</div>
            <div className="logo-text">
              <h1>AlignSmart</h1>
              <p>Dental Monitoring System</p>
            </div>
          </div>
          <div className="header-info">
            <span className="version">v1.0.0</span>
            <span className="status online">● Online</span>
            <button 
              onClick={() => setShowDemo(true)}
              className="help-btn"
              title="View Demo Guide"
            >
              <HelpCircle size={20} />
            </button>
          </div>
        </div>
      </header>
      
      <main className="app-main">
        <DentalScanner />
      </main>
      
      <footer className="app-footer">
        <p>© 2024 AlignSmart - Professional Dental Monitoring for Aligner Treatment</p>
      </footer>

      {showDemo && <DemoGuide onClose={() => setShowDemo(false)} />}
    </div>
  );
}

export default App;
