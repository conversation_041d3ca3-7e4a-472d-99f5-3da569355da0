import React, { useState, useEffect } from 'react';
import { Toaster } from 'react-hot-toast';
import { HelpCircle } from 'lucide-react';
import DentalScanner from './components/DentalScanner';
import MobileDashboard from './components/MobileDashboard';
import MobileScanner from './components/MobileScanner';
import DemoGuide from './components/DemoGuide';
import './App.css';

function App() {
  const [showDemo, setShowDemo] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [isScanning, setIsScanning] = useState(false);
  const [scanHistory, setScanHistory] = useState([]);

  // Mobile detection
  useEffect(() => {
    const checkMobile = () => {
      const userAgent = navigator.userAgent || navigator.vendor || window.opera;
      const isMobileDevice = /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(userAgent.toLowerCase());
      const isSmallScreen = window.innerWidth <= 768;
      setIsMobile(isMobileDevice || isSmallScreen);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  const startScanning = () => {
    setIsScanning(true);
  };

  const handleScanComplete = (scans) => {
    setScanHistory(prev => [...prev, ...scans]);
    setIsScanning(false);
  };

  const handleScanExit = () => {
    setIsScanning(false);
  };

  // Mobile view
  if (isMobile) {
    return (
      <div className="App mobile-app">
        <Toaster
          position="top-center"
          toastOptions={{
            duration: 3000,
            style: {
              background: '#363636',
              color: '#fff',
              fontSize: '14px',
            },
          }}
        />

        {isScanning ? (
          <MobileScanner
            onComplete={handleScanComplete}
            onExit={handleScanExit}
          />
        ) : (
          <>
            <header className="mobile-header">
              <div className="mobile-header-content">
                <div className="logo-section">
                  <div className="logo-icon">🦷</div>
                  <div className="logo-text">
                    <h1>AlignSmart</h1>
                    <p>Dental Monitoring</p>
                  </div>
                </div>
                <button
                  onClick={() => setShowDemo(true)}
                  className="help-btn"
                  title="View Demo Guide"
                >
                  <HelpCircle size={20} />
                </button>
              </div>
            </header>

            <main className="mobile-main">
              <MobileDashboard
                onStartScanning={startScanning}
                scanHistory={scanHistory}
              />
            </main>
          </>
        )}

        {showDemo && <DemoGuide onClose={() => setShowDemo(false)} />}
      </div>
    );
  }

  // Desktop view (existing)
  return (
    <div className="App">
      <Toaster
        position="top-right"
        toastOptions={{
          duration: 4000,
          style: {
            background: '#363636',
            color: '#fff',
          },
        }}
      />
      <header className="app-header">
        <div className="header-content">
          <div className="logo-section">
            <div className="logo-icon">🦷</div>
            <div className="logo-text">
              <h1>AlignSmart</h1>
              <p>Dental Monitoring System</p>
            </div>
          </div>
          <div className="header-info">
            <span className="version">v1.0.0</span>
            <span className="status online">● Online</span>
            <button
              onClick={() => setShowDemo(true)}
              className="help-btn"
              title="View Demo Guide"
            >
              <HelpCircle size={20} />
            </button>
          </div>
        </div>
      </header>

      <main className="app-main">
        <DentalScanner />
      </main>

      <footer className="app-footer">
        <p>© 2024 AlignSmart - Professional Dental Monitoring for Aligner Treatment</p>
      </footer>

      {showDemo && <DemoGuide onClose={() => setShowDemo(false)} />}
    </div>
  );
}

export default App;
