import React from 'react';
import { motion } from 'framer-motion';
import { Camera, User, Calendar, Download, History, Settings } from 'lucide-react';
import './MobileDashboard.css';

const MobileDashboard = ({ onStartScanning, scanHistory = [] }) => {
  const formatDate = (date) => {
    return new Date(date).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const recentScans = scanHistory.slice(-3);

  return (
    <div className="mobile-dashboard">
      {/* Welcome Section */}
      <motion.div 
        className="welcome-section"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <div className="welcome-content">
          <h2>Welcome Back!</h2>
          <p>Ready for your dental monitoring session?</p>
        </div>
        <div className="patient-info">
          <User size={20} />
          <span>Patient Dashboard</span>
        </div>
      </motion.div>

      {/* Quick Stats */}
      <motion.div 
        className="stats-grid"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.1 }}
      >
        <div className="stat-card">
          <div className="stat-icon">
            <Camera size={24} />
          </div>
          <div className="stat-content">
            <h3>{scanHistory.length}</h3>
            <p>Total Scans</p>
          </div>
        </div>
        
        <div className="stat-card">
          <div className="stat-icon">
            <Calendar size={24} />
          </div>
          <div className="stat-content">
            <h3>{scanHistory.length > 0 ? formatDate(scanHistory[scanHistory.length - 1].timestamp).split(',')[0] : 'None'}</h3>
            <p>Last Scan</p>
          </div>
        </div>
      </motion.div>

      {/* Main Action Button */}
      <motion.div 
        className="main-action"
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.6, delay: 0.2 }}
      >
        <motion.button
          className="start-scan-btn"
          onClick={onStartScanning}
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
        >
          <div className="btn-icon">
            <Camera size={32} />
          </div>
          <div className="btn-content">
            <h3>Start Scanning</h3>
            <p>Begin your dental monitoring session</p>
          </div>
        </motion.button>
      </motion.div>

      {/* Recent Scans */}
      {scanHistory.length > 0 && (
        <motion.div 
          className="recent-scans"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
        >
          <h3>Recent Scans</h3>
          
          <div className="scan-list">
            {recentScans.map((scan, index) => (
              <motion.div 
                key={scan.id}
                className="scan-item"
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.4, delay: 0.1 * index }}
              >
                <div className="scan-thumbnail">
                  <img src={scan.imageData} alt={scan.regionName} />
                </div>
                <div className="scan-details">
                  <h4>{scan.regionName}</h4>
                  <p>{formatDate(scan.timestamp)}</p>
                </div>
                <a 
                  href={scan.imageData} 
                  download={`${scan.regionName}_scan.jpg`}
                  className="download-btn"
                >
                  <Download size={16} />
                </a>
              </motion.div>
            ))}
          </div>
          
          {scanHistory.length > 3 && (
            <button className="view-all-btn">
              View All Scans ({scanHistory.length})
            </button>
          )}
        </motion.div>
      )}

      {/* Quick Actions */}
      <motion.div 
        className="quick-actions"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.4 }}
      >
        <h3>Quick Actions</h3>
        <div className="action-grid">
          <button className="action-btn">
            <History size={20} />
            <span>View History</span>
          </button>
          <button className="action-btn">
            <Settings size={20} />
            <span>Settings</span>
          </button>
        </div>
      </motion.div>
    </div>
  );
};

export default MobileDashboard;
